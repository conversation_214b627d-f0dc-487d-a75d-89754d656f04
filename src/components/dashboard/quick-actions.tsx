'use client'

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { FileText, Users, FolderOpen, BarChart3 } from "lucide-react"
import { DashboardData } from "@/lib/api/dashboard"
import Link from "next/link"

interface QuickActionsProps {
  data: DashboardData
}

const iconMap = {
  FileText,
  Users,
  FolderOpen,
  BarChart3
}

export function QuickActions({ data }: QuickActionsProps) {
  return (
    <Card className="border-border/50 shadow-sm">
      <CardHeader className="space-y-2">
        <CardTitle className="text-xl font-semibold tracking-tight">Quick Actions</CardTitle>
        <CardDescription className="text-muted-foreground/80">
          Common tasks to help you manage your business efficiently
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {data.quickActions.map((action) => {
            const IconComponent = iconMap[action.icon as keyof typeof iconMap]

            return (
              <Link key={action.title} href={action.href}>
                <Button
                  variant="outline"
                  className="h-auto p-6 flex flex-col items-center space-y-3 w-full hover:bg-accent/50 hover:border-primary/20 transition-all duration-200 group"
                >
                  <div className="h-12 w-12 rounded-xl bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                    <IconComponent className="h-6 w-6 text-primary" />
                  </div>
                  <div className="text-center space-y-1">
                    <div className="font-medium text-sm">{action.title}</div>
                    <div className="text-xs text-muted-foreground/80">{action.description}</div>
                  </div>
                </Button>
              </Link>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
