"use client"

import { useState, useEffect, useCallback } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { StatusBadge } from "@/components/ui/status-badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { getInvoices, searchInvoices, getInvoicesByStatus, deleteInvoice } from "@/lib/api/invoices-client"
import { InvoiceWithRelations } from "@/lib/types"
import { formatInvoiceCurrency, type Currency } from "@/lib/utils/currency"
import { Search, Plus, MoreHorizontal, Edit, Trash2, Eye, Calendar, FileText, Loader2 } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { InvoiceForm } from "./invoice-form"

interface InvoiceListProps {
  preselectedClientId?: string
  preselectedProjectId?: string
}

export function InvoiceList({ preselectedClientId, preselectedProjectId }: InvoiceListProps) {
  const [invoices, setInvoices] = useState<InvoiceWithRelations[]>([])
  const [loading, setLoading] = useState(true)
  const [searchInput, setSearchInput] = useState("")
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [showInvoiceForm, setShowInvoiceForm] = useState(false)
  const [editingInvoice, setEditingInvoice] = useState<InvoiceWithRelations | undefined>()

  // Debounce search input
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      // Only search if input has 3+ characters or is empty
      if (searchInput.length === 0 || searchInput.length >= 3) {
        setDebouncedSearchQuery(searchInput)
      }
    }, 500) // 500ms debounce delay

    return () => {
      clearTimeout(timeoutId)
    }
  }, [searchInput])

  const fetchInvoices = useCallback(async () => {
    setLoading(true)
    try {
      let data: InvoiceWithRelations[]

      if (debouncedSearchQuery.trim()) {
        data = await searchInvoices(debouncedSearchQuery)
      } else if (statusFilter !== "all") {
        data = await getInvoicesByStatus(statusFilter)
      } else {
        data = await getInvoices()
      }

      setInvoices(data)
    } catch (error) {
      console.error("Failed to fetch invoices:", error)
    } finally {
      setLoading(false)
    }
  }, [debouncedSearchQuery, statusFilter])

  useEffect(() => {
    fetchInvoices()
  }, [fetchInvoices])

  const handleSearch = useCallback((query: string) => {
    setSearchInput(query)
  }, [])

  const handleStatusFilter = useCallback((status: string) => {
    setStatusFilter(status)
  }, [])

  const handleEdit = (invoice: InvoiceWithRelations) => {
    setEditingInvoice(invoice)
    setShowInvoiceForm(true)
  }

  const handleDelete = async (invoice: InvoiceWithRelations) => {
    if (window.confirm(`Are you sure you want to delete invoice ${invoice.invoice_number}?`)) {
      try {
        await deleteInvoice(invoice.id)
        fetchInvoices()
      } catch (error) {
        console.error("Failed to delete invoice:", error)
        alert("Failed to delete invoice. Please try again.")
      }
    }
  }

  const handleFormSuccess = () => {
    fetchInvoices()
    setEditingInvoice(undefined)
  }

  const handleFormClose = () => {
    setShowInvoiceForm(false)
    setEditingInvoice(undefined)
  }





  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">Loading invoices...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Search and Filter Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search invoices..."
            value={searchInput}
            onChange={(e) => handleSearch(e.target.value)}
            className="pl-10 h-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={handleStatusFilter}>
          <SelectTrigger className="w-full sm:w-[180px] h-10">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="sent">Sent</SelectItem>
            <SelectItem value="paid">Paid</SelectItem>
            <SelectItem value="overdue">Overdue</SelectItem>
            <SelectItem value="cancelled">Cancelled</SelectItem>
          </SelectContent>
        </Select>
        <Button onClick={() => setShowInvoiceForm(true)} className="h-10 px-6">
          <Plus className="mr-2 h-4 w-4" />
          Create Invoice
        </Button>
      </div>

      {/* Invoice List */}
      {loading ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </CardContent>
        </Card>
      ) : invoices.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No invoices found</h3>
            <p className="text-muted-foreground text-center mb-4">
              {debouncedSearchQuery || statusFilter !== "all"
                ? "Try adjusting your search or filter criteria."
                : "Create your first invoice to get started."
              }
            </p>
            {searchInput.length > 0 && searchInput.length < 3 && (
              <p className="text-sm text-muted-foreground mt-2">
                Type at least 3 characters to search
              </p>
            )}
            <Button onClick={() => setShowInvoiceForm(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Invoice
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Card className="border-border/50 shadow-sm">
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow className="border-b border-border/40">
                  <TableHead>Invoice #</TableHead>
                  <TableHead>Client</TableHead>
                  <TableHead className="hidden md:table-cell">Project</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                  <TableHead className="hidden sm:table-cell">Status</TableHead>
                  <TableHead className="hidden lg:table-cell">Due Date</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invoices.map((invoice) => (
                  <TableRow key={invoice.id}>
                    <TableCell className="font-medium">
                      <div className="flex flex-col">
                        <Link
                          href={`/invoices/${invoice.id}`}
                          className="hover:text-primary transition-colors cursor-pointer"
                        >
                          {invoice.invoice_number}
                        </Link>
                        {invoice.milestone_type && invoice.milestone_type !== 'standard' && (
                          <Badge variant="secondary" className="text-xs w-fit mt-1">
                            {invoice.milestone_type === 'dp' && 'DP'}
                            {invoice.milestone_type === 'progress' && 'Progress'}
                            {invoice.milestone_type === 'final' && 'Final'}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">{invoice.client?.name}</span>
                        {invoice.client?.company && (
                          <span className="text-sm text-muted-foreground">{invoice.client.company}</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      {invoice.project ? (
                        <span className="text-sm">{invoice.project.name}</span>
                      ) : (
                        <span className="text-sm text-muted-foreground">No project</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right font-medium">
                      {formatInvoiceCurrency(invoice.total_amount, invoice.currency as Currency)}
                    </TableCell>
                    <TableCell className="hidden sm:table-cell">
                      <StatusBadge status={invoice.status} />
                    </TableCell>
                    <TableCell className="hidden lg:table-cell">
                      {invoice.due_date ? (
                        <div className="flex items-center gap-1 text-sm">
                          <Calendar className="h-3 w-3" />
                          {formatDate(invoice.due_date)}
                        </div>
                      ) : (
                        <span className="text-sm text-muted-foreground">No due date</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        <Link href={`/invoices/${invoice.id}`}>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4" />
                            <span className="sr-only">View</span>
                          </Button>
                        </Link>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">More actions</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEdit(invoice)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDelete(invoice)}
                              className="text-destructive"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Invoice Form Dialog */}
      <InvoiceForm
        open={showInvoiceForm}
        onOpenChange={handleFormClose}
        invoice={editingInvoice}
        onSuccess={handleFormSuccess}
        preselectedClientId={preselectedClientId}
        preselectedProjectId={preselectedProjectId}
      />
    </div>
  )
}
