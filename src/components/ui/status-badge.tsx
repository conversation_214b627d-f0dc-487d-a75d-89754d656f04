import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { ClientStatus, ProjectStatus, InvoiceStatus } from "@/lib/types"

interface StatusBadgeProps {
  status: ClientStatus | ProjectStatus | InvoiceStatus
  className?: string
}

export function StatusBadge({ status, className }: StatusBadgeProps) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      // Client statuses
      case "lead":
        return {
          label: "Lead",
          className: "status-badge-lead",
        }
      case "active":
        return {
          label: "Active",
          className: "status-badge-active",
        }
      case "inactive":
        return {
          label: "Inactive",
          className: "status-badge-inactive",
        }
      case "archived":
        return {
          label: "Archived",
          className: "status-badge-inactive",
        }
      
      // Project statuses
      case "planning":
        return {
          label: "Planning",
          className: "bg-purple-50 text-purple-700 border border-purple-200 dark:bg-purple-950 dark:text-purple-300 dark:border-purple-800",
        }
      case "in_progress":
        return {
          label: "In Progress",
          className: "status-badge-pending",
        }
      case "review":
        return {
          label: "Review",
          className: "bg-orange-50 text-orange-700 border border-orange-200 dark:bg-orange-950 dark:text-orange-300 dark:border-orange-800",
        }
      case "completed":
        return {
          label: "Completed",
          className: "status-badge-active",
        }
      case "cancelled":
        return {
          label: "Cancelled",
          className: "status-badge-overdue",
        }
      
      // Invoice statuses
      case "draft":
        return {
          label: "Draft",
          className: "status-badge-inactive",
        }
      case "sent":
        return {
          label: "Sent",
          className: "status-badge-pending",
        }
      case "paid":
        return {
          label: "Paid",
          className: "status-badge-active",
        }
      case "overdue":
        return {
          label: "Overdue",
          className: "status-badge-overdue",
        }
      
      default:
        return {
          label: status.charAt(0).toUpperCase() + status.slice(1),
          className: "status-badge-inactive",
        }
    }
  }

  const config = getStatusConfig(status)

  return (
    <Badge
      variant="secondary"
      className={cn(
        "px-3 py-1 text-xs font-medium rounded-full",
        config.className,
        className
      )}
    >
      {config.label}
    </Badge>
  )
}
