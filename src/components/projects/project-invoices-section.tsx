"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { StatusBadge } from "@/components/ui/status-badge"
import { getInvoicesByProject } from "@/lib/api/invoices-client"
import { InvoiceWithClient } from "@/lib/types"
import { formatInvoiceCurrency } from "@/lib/utils/currency"
import { FileText, Plus, Calendar, DollarSign } from "lucide-react"
import { format } from "date-fns"

interface ProjectInvoicesSectionProps {
  projectId: string
  projectName: string
}

export function ProjectInvoicesSection({ projectId }: ProjectInvoicesSectionProps) {
  const [invoices, setInvoices] = useState<InvoiceWithClient[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchInvoices = async () => {
      setLoading(true)
      setError(null)
      try {
        // We need to create this function to get invoices by project_id
        const data = await getInvoicesByProject(projectId)
        setInvoices(data)
      } catch (err) {
        console.error("Failed to fetch project invoices:", err)
        setError("Failed to load invoices")
      } finally {
        setLoading(false)
      }
    }

    fetchInvoices()
  }, [projectId])

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Invoices
          </CardTitle>
          <CardDescription>
            All invoices associated with this project
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">Loading invoices...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Invoices
          </CardTitle>
          <CardDescription>
            All invoices associated with this project
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-destructive">{error}</p>
            <Button 
              variant="outline" 
              className="mt-4"
              onClick={() => window.location.reload()}
            >
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Invoices
            </CardTitle>
            <CardDescription>
              All invoices associated with this project
            </CardDescription>
          </div>
          <Button asChild size="sm">
            <Link href={`/invoices/new?project=${projectId}`}>
              <Plus className="mr-2 h-4 w-4" />
              Create Invoice
            </Link>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {invoices.length > 0 ? (
          <div className="space-y-3">
            {invoices.map((invoice) => (
              <Link 
                key={invoice.id} 
                href={`/invoices/${invoice.id}`}
                className="block"
              >
                <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <div>
                        <p className="font-medium text-sm">
                          Invoice {invoice.invoice_number}
                        </p>
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          {invoice.invoice_date && (
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              <span>
                                {format(new Date(invoice.invoice_date), "MMM dd, yyyy")}
                              </span>
                            </div>
                          )}
                          {invoice.due_date && (
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              <span>
                                Due: {format(new Date(invoice.due_date), "MMM dd, yyyy")}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="text-right">
                      <div className="flex items-center gap-1 text-sm font-medium">
                        <DollarSign className="h-3 w-3" />
                        <span>
                          {formatInvoiceCurrency(invoice.total_amount, invoice.currency)}
                        </span>
                      </div>
                    </div>
                    <StatusBadge status={invoice.status} />
                  </div>
                </div>
              </Link>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <div className="mx-auto w-12 h-12 bg-muted rounded-lg flex items-center justify-center mb-4">
              <FileText className="h-6 w-6 text-muted-foreground" />
            </div>
            <p className="text-muted-foreground mb-4">No invoices yet for this project.</p>
            <Button asChild variant="outline">
              <Link href={`/invoices/new?project=${projectId}`}>
                <Plus className="mr-2 h-4 w-4" />
                Create First Invoice
              </Link>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
