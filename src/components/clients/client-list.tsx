"use client"

import { useState, useEffect, useCallback } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { StatusBadge } from "@/components/ui/status-badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { getClients, searchClients, getClientsByStatus, deleteClient } from "@/lib/api/clients-client"
import { Client, ClientStatus } from "@/lib/types"
import { Search, Plus, MoreHorizontal, Edit, Trash2, Eye, Loader2 } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ClientForm } from "./client-form"

interface ClientListProps {
  onClientSelect?: (client: Client) => void
}

export function ClientList({}: ClientListProps) {
  const [clients, setClients] = useState<Client[]>([])
  const [loading, setLoading] = useState(true)
  const [searchInput, setSearchInput] = useState("")
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("")
  const [isSearching, setIsSearching] = useState(false)
  const [statusFilter, setStatusFilter] = useState<ClientStatus | "all">("all")
  const [showForm, setShowForm] = useState(false)
  const [editingClient, setEditingClient] = useState<Client | undefined>()

  // Debounce search input
  useEffect(() => {
    setIsSearching(true)
    const timeoutId = setTimeout(() => {
      // Only search if input has 3+ characters or is empty
      if (searchInput.length === 0 || searchInput.length >= 3) {
        setDebouncedSearchQuery(searchInput)
      }
      setIsSearching(false)
    }, 500) // 500ms debounce delay

    return () => {
      clearTimeout(timeoutId)
    }
  }, [searchInput])

  const fetchClients = useCallback(async () => {
    setLoading(true)
    try {
      let data
      if (debouncedSearchQuery) {
        data = await searchClients(debouncedSearchQuery)
      } else if (statusFilter !== "all") {
        data = await getClientsByStatus(statusFilter)
      } else {
        data = await getClients()
      }
      setClients(data)
    } catch (error) {
      console.error("Failed to fetch clients:", error)
    } finally {
      setLoading(false)
    }
  }, [debouncedSearchQuery, statusFilter])

  useEffect(() => {
    fetchClients()
  }, [fetchClients])

  const handleDeleteClient = async (id: string) => {
    if (confirm("Are you sure you want to delete this client?")) {
      try {
        await deleteClient(id)
        fetchClients()
      } catch (error) {
        console.error("Failed to delete client:", error)
      }
    }
  }

  const handleEditClient = (client: Client) => {
    setEditingClient(client)
    setShowForm(true)
  }

  const handleFormSuccess = () => {
    fetchClients()
    setEditingClient(undefined)
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-2">
                <div className="h-4 bg-muted rounded w-1/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
                <div className="h-3 bg-muted rounded w-1/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Search and Filter Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          {isSearching && searchInput.length >= 3 && (
            <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4 animate-spin" />
          )}
          <Input
            placeholder="Search clients by name, email, or company (min 3 characters)..."
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            className="pl-10 pr-10 h-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={(value: ClientStatus | "all") => setStatusFilter(value)}>
          <SelectTrigger className="w-full sm:w-[180px] h-10">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="lead">Leads</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
            <SelectItem value="archived">Archived</SelectItem>
          </SelectContent>
        </Select>
        <Button onClick={() => setShowForm(true)} className="h-10 px-6">
          <Plus className="mr-2 h-4 w-4" />
          Add Client
        </Button>
      </div>

      {/* Client List */}
      {loading ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </CardContent>
        </Card>
      ) : clients.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground">
              {debouncedSearchQuery || statusFilter !== "all"
                ? "No clients found matching your criteria."
                : "No clients yet. Add your first client to get started."
              }
            </p>
            {searchInput.length > 0 && searchInput.length < 3 && (
              <p className="text-sm text-muted-foreground mt-2">
                Type at least 3 characters to search
              </p>
            )}
          </CardContent>
        </Card>
      ) : (
        <Card className="border-border/50 shadow-sm">
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow className="border-b border-border/40">
                  <TableHead>Name</TableHead>
                  <TableHead className="hidden md:table-cell">Company</TableHead>
                  <TableHead className="hidden sm:table-cell">Email</TableHead>
                  <TableHead className="hidden lg:table-cell">Phone</TableHead>
                  <TableHead className="hidden xl:table-cell">Website</TableHead>
                  <TableHead className="hidden sm:table-cell">Status</TableHead>
                  <TableHead className="hidden lg:table-cell">Source</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {clients.map((client) => (
                  <TableRow key={client.id}>
                    <TableCell className="font-medium">
                      <div className="flex flex-col">
                        <Link
                          href={`/clients/${client.id}`}
                          className="hover:text-primary transition-colors cursor-pointer font-medium text-sm"
                        >
                          {client.name}
                        </Link>
                      </div>
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      {client.company ? (
                        <span className="text-sm">{client.company}</span>
                      ) : (
                        <span className="text-sm text-muted-foreground">No company</span>
                      )}
                    </TableCell>
                    <TableCell className="hidden sm:table-cell">
                      {client.email ? (
                        <span className="text-sm">{client.email}</span>
                      ) : (
                        <span className="text-sm text-muted-foreground">No email</span>
                      )}
                    </TableCell>
                    <TableCell className="hidden lg:table-cell">
                      {client.phone ? (
                        <span className="text-sm">{client.phone}</span>
                      ) : (
                        <span className="text-sm text-muted-foreground">No phone</span>
                      )}
                    </TableCell>
                    <TableCell className="hidden xl:table-cell">
                      {client.website ? (
                        <a
                          href={client.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-primary hover:underline"
                        >
                          {client.website.replace(/^https?:\/\//, '')}
                        </a>
                      ) : (
                        <span className="text-sm text-muted-foreground">No website</span>
                      )}
                    </TableCell>
                    <TableCell className="hidden sm:table-cell">
                      <StatusBadge status={client.status} />
                    </TableCell>
                    <TableCell className="hidden lg:table-cell">
                      {client.lead_source ? (
                        <span className="text-sm">{client.lead_source}</span>
                      ) : (
                        <span className="text-sm text-muted-foreground">No source</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">More actions</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/clients/${client.id}`}>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEditClient(client)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDeleteClient(client.id)}
                            className="text-destructive"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Client Form Dialog */}
      <ClientForm
        open={showForm}
        onOpenChange={setShowForm}
        client={editingClient}
        onSuccess={handleFormSuccess}
      />
    </div>
  )
}
