'use client'

import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { Building2 } from "lucide-react"

export function DashboardHeader() {
  return (
    <header className="sticky top-0 z-50 flex h-16 shrink-0 items-center gap-3 border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-6">
      <SidebarTrigger className="-ml-1 hover:bg-accent/50 transition-colors" />
      <Separator orientation="vertical" className="mr-2 h-4 bg-border/60" />

      {/* Mobile Logo - enhanced with better spacing and modern styling */}
      <div className="flex items-center gap-3 md:hidden">
        <div className="flex h-9 w-9 items-center justify-center rounded-xl bg-gradient-to-br from-primary to-primary/80 text-primary-foreground shadow-sm">
          <Building2 className="h-4 w-4" />
        </div>
        <div className="flex flex-col">
          <span className="text-sm font-semibold tracking-tight">HarunStudio</span>
          <span className="text-xs text-muted-foreground/80">Business Management</span>
        </div>
      </div>

      <div className="flex-1">
        {/* Header content can be added here */}
      </div>
    </header>
  )
}
