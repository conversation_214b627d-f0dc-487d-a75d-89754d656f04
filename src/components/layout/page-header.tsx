import { cn } from "@/lib/utils"

interface PageHeaderProps {
  title: string
  description?: string
  children?: React.ReactNode
  className?: string
}

export function PageHeader({
  title,
  description,
  children,
  className,
}: PageHeaderProps) {
  return (
    <div className={cn("flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0", className)}>
      <div className="space-y-2">
        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
          {title}
        </h1>
        {description && (
          <p className="text-sm sm:text-base text-muted-foreground/80">{description}</p>
        )}
      </div>
      {children && (
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-3">
          {children}
        </div>
      )}
    </div>
  )
}
