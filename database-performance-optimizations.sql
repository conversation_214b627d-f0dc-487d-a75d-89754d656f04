-- HarunStudio Database Performance Optimization Script
-- Execute these commands in Supabase SQL Editor
-- Based on Performance Audit Report recommendations

-- =============================================================================
-- PHASE 1: CRITICAL DATABASE INDEXES FOR RLS PERFORMANCE
-- Expected Impact: 50-80% query performance improvement
-- =============================================================================

-- Priority 1: RLS Performance Indexes
-- These indexes support Row Level Security policies and user-based filtering

-- Index for clients.assigned_to (used in RLS policies)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_clients_assigned_to 
ON clients(assigned_to) 
WHERE assigned_to IS NOT NULL;

-- Index for projects.client_id (foreign key, used in joins and RLS)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_client_id 
ON projects(client_id);

-- Index for projects.created_by (used in RLS policies)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_created_by 
ON projects(created_by);

-- Index for invoices.client_id (foreign key, used in joins and RLS)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_client_id 
ON invoices(client_id);

-- Index for invoices.project_id (foreign key, used in joins)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_project_id 
ON invoices(project_id) 
WHERE project_id IS NOT NULL;

-- Index for invoices.created_by (used in RLS policies)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_created_by 
ON invoices(created_by);

-- Index for activities.user_id (used in RLS policies)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_user_id 
ON activities(user_id);

-- =============================================================================
-- PHASE 2: SEARCH PERFORMANCE INDEXES
-- Expected Impact: 75% search response improvement (800ms → 200ms)
-- =============================================================================

-- Full-text search index for clients
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_clients_search 
ON clients USING gin(
  to_tsvector('english',
    COALESCE(name, '') || ' ' ||
    COALESCE(company, '') || ' ' ||
    COALESCE(email, '') || ' ' ||
    COALESCE(phone, '')
  )
);

-- Full-text search index for projects
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_search 
ON projects USING gin(
  to_tsvector('english',
    COALESCE(name, '') || ' ' ||
    COALESCE(description, '')
  )
);

-- =============================================================================
-- PHASE 3: ANALYTICS AND DASHBOARD QUERY INDEXES
-- Expected Impact: 40% dashboard load time reduction
-- =============================================================================

-- Composite index for invoice analytics (status + date filtering)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_status_date 
ON invoices(status, invoice_date);

-- Composite index for project analytics (status + creation date)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_status_created 
ON projects(status, created_at);

-- Composite index for client analytics (status + creation date)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_clients_status_created 
ON clients(status, created_at);

-- Index for invoice date-based queries (revenue charts)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_invoice_date 
ON invoices(invoice_date) 
WHERE invoice_date IS NOT NULL;

-- =============================================================================
-- PHASE 4: COMPOSITE INDEXES FOR COMMON QUERY PATTERNS
-- Expected Impact: Optimized multi-table queries and filtering
-- =============================================================================

-- Composite index for client-specific invoice queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_client_status 
ON invoices(client_id, status);

-- Composite index for client-specific project queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_client_status 
ON projects(client_id, status);

-- Composite index for project timeline queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_dates 
ON projects(start_date, end_date) 
WHERE start_date IS NOT NULL;

-- Composite index for invoice payment tracking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_payment_tracking 
ON invoices(status, due_date, paid_date);

-- =============================================================================
-- PHASE 5: ACTIVITY LOG PERFORMANCE INDEXES
-- Expected Impact: Faster activity tracking and audit queries
-- =============================================================================

-- Composite index for entity-specific activity queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_entity 
ON activities(entity_type, entity_id, created_at);

-- Index for recent activities (dashboard)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_recent 
ON activities(created_at DESC);

-- =============================================================================
-- VERIFICATION QUERIES
-- Run these to verify index creation and usage
-- =============================================================================

-- Check if indexes were created successfully
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename IN ('clients', 'projects', 'invoices', 'activities')
    AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;

-- Check index sizes
SELECT 
    schemaname,
    tablename,
    indexname,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
    AND indexrelname LIKE 'idx_%'
ORDER BY pg_relation_size(indexrelid) DESC;

-- =============================================================================
-- NOTES FOR EXECUTION
-- =============================================================================

-- 1. Execute indexes one by one to monitor progress
-- 2. CONCURRENTLY option allows index creation without blocking table access
-- 3. Monitor system resources during index creation on large tables
-- 4. Verify query performance improvements using EXPLAIN ANALYZE
-- 5. Consider running during low-traffic periods for large tables

-- Example performance test query:
-- EXPLAIN ANALYZE SELECT * FROM clients WHERE assigned_to = 'user-id';
