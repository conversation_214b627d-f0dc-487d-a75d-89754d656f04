import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // =============================================================================
  // IMAGE OPTIMIZATION
  // =============================================================================
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*.supabase.co',
        port: '',
        pathname: '/storage/v1/object/public/**',
      },
    ],
    // Optimize image caching for better performance
    minimumCacheTTL: 2678400, // 31 days
  },

  // =============================================================================
  // ENVIRONMENT VARIABLES
  // =============================================================================
  env: {
    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
    NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  },

  // =============================================================================
  // EXPERIMENTAL FEATURES (Latest Next.js 15+ optimizations)
  // =============================================================================
  experimental: {
    // React Compiler for better optimization (Next.js 15+)
    reactCompiler: true,

    // Optimize package imports for better tree-shaking
    optimizePackageImports: [
      'recharts',
      'date-fns',
      'lucide-react',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-select',
      '@radix-ui/react-toast',
      '@radix-ui/react-tooltip',
      '@radix-ui/react-popover',
      '@radix-ui/react-tabs',
      '@radix-ui/react-accordion',
      '@radix-ui/react-checkbox',
      '@radix-ui/react-radio-group',
      '@radix-ui/react-switch',
      '@radix-ui/react-slider',
      '@radix-ui/react-progress',
      '@radix-ui/react-avatar',
      '@radix-ui/react-badge',
      '@radix-ui/react-button',
      '@radix-ui/react-card',
      '@radix-ui/react-input',
      '@radix-ui/react-label',
      '@radix-ui/react-textarea',
      'class-variance-authority',
      'clsx',
      'tailwind-merge'
    ],

    // Webpack memory optimizations (Next.js 15+)
    webpackMemoryOptimizations: true,

    // Lightning CSS for faster CSS processing
    useLightningcss: true,

    // Partial Prerendering for better performance
    ppr: true,

    // Client-side router cache optimization
    staleTimes: {
      dynamic: 30,    // 30 seconds for dynamic pages
      static: 180,    // 3 minutes for static pages
    },

    // Dynamic IO for better streaming
    dynamicIO: true,

    // CSS chunking optimization
    cssChunking: true,

    // Inline CSS for critical path optimization
    inlineCss: true,
  },

  // =============================================================================
  // SERVER OPTIMIZATIONS
  // =============================================================================
  // Externalize packages that should only run on server
  serverExternalPackages: ['puppeteer'],

  // =============================================================================
  // PERFORMANCE OPTIMIZATIONS
  // =============================================================================
  // Enable compression for better performance
  compress: true,

  // Generate ETags for better caching
  generateEtags: true,

  // Disable powered by header for security
  poweredByHeader: false,

  // Enable production source maps for debugging (can be disabled in production)
  productionBrowserSourceMaps: process.env.NODE_ENV === 'development',

  // React Strict Mode for better development experience
  reactStrictMode: true,

  // =============================================================================
  // WEBPACK CUSTOMIZATION
  // =============================================================================
  webpack: (config, { isServer, dev }) => {
    // Bundle analyzer for development
    if (process.env.ANALYZE === 'true') {
      const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
      config.plugins.push(
        new BundleAnalyzerPlugin({
          analyzerMode: 'server',
          analyzerPort: isServer ? 8888 : 8889,
          openAnalyzer: true,
        })
      );
    }

    // Optimize for production builds
    if (!dev && !isServer) {
      // Split chunks for better caching
      config.optimization.splitChunks = {
        ...config.optimization.splitChunks,
        cacheGroups: {
          ...config.optimization.splitChunks.cacheGroups,
          // Separate chunk for chart libraries
          charts: {
            name: 'charts',
            test: /[\\/]node_modules[\\/](recharts|d3)[\\/]/,
            chunks: 'all',
            priority: 30,
          },
          // Separate chunk for UI libraries
          ui: {
            name: 'ui',
            test: /[\\/]node_modules[\\/](@radix-ui)[\\/]/,
            chunks: 'all',
            priority: 25,
          },
          // Separate chunk for utilities
          utils: {
            name: 'utils',
            test: /[\\/]node_modules[\\/](date-fns|clsx|class-variance-authority|tailwind-merge)[\\/]/,
            chunks: 'all',
            priority: 20,
          },
        },
      };
    }

    return config;
  },
};

export default nextConfig;