name: Deploy to VPS

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to VPS
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.SSH_HOST }}
        username: ${{ secrets.SSH_USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        port: ${{ secrets.SSH_PORT }}
        script: |
          cd ~/app.harunstudio.com
          git pull origin main
          npm install
          if npm run build; then
            pm2 restart harunstudio-app || pm2 start npm --name "harunstudio-app" -- start
            echo "Deploy berhasil!"
          else
            echo "Build gagal, rollback ke commit sebelumnya"
            git reset --hard HEAD~1
            npm run build
            pm2 restart harunstudio-app
            exit 1
          fi