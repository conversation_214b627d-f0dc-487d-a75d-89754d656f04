# PDF Generation Setup Guide

This guide explains how to set up high-quality PDF generation for HarunStudio invoices using Puppeteer.

## 🎯 **Why Puppeteer?**

We chose Puppeteer over other solutions because:
- ✅ **High Quality**: Renders exactly like the browser with perfect typography
- ✅ **Professional Output**: Supports complex layouts, fonts, and styling
- ✅ **Indonesian Support**: Excellent Unicode and localization support
- ✅ **Reliable**: Battle-tested by Google Chrome team
- ✅ **Flexible**: Easy to customize and extend

## 📦 **Installation Steps**

### 1. Install Puppeteer

```bash
npm install puppeteer
npm install @types/puppeteer --save-dev
```

### 2. Update PDF Generator Implementation

Replace the placeholder in `src/lib/pdf/invoice-pdf-generator.ts`:

```typescript
import puppeteer from 'puppeteer'

export class InvoicePDFGenerator {
  static async generatePDF(
    data: InvoicePDFData, 
    options: Partial<PDFGenerationOptions> = {}
  ): Promise<Buffer> {
    const mergedOptions = { ...this.defaultOptions, ...options }
    
    // Launch browser
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    })
    
    try {
      const page = await browser.newPage()
      
      // Generate HTML content
      const html = this.generateInvoiceHTML(data)
      
      // Set content and wait for fonts to load
      await page.setContent(html, { 
        waitUntil: 'networkidle0' 
      })
      
      // Generate PDF
      const pdfBuffer = await page.pdf({
        format: mergedOptions.format,
        orientation: mergedOptions.orientation,
        margin: mergedOptions.margin,
        displayHeaderFooter: mergedOptions.displayHeaderFooter,
        headerTemplate: mergedOptions.headerTemplate,
        footerTemplate: mergedOptions.footerTemplate,
        printBackground: mergedOptions.printBackground,
        scale: mergedOptions.scale,
      })
      
      return Buffer.from(pdfBuffer)
    } finally {
      await browser.close()
    }
  }
}
```

### 3. Update API Route

Uncomment the PDF generation code in `src/app/api/invoices/[id]/pdf/route.ts`:

```typescript
// Generate PDF
const pdfBuffer = await InvoicePDFGenerator.generatePDF({
  invoice: data.invoice,
  company: data.company,
  template: data.template,
  bankAccounts: data.bankAccounts
})

// Return PDF response
return new NextResponse(pdfBuffer, {
  headers: {
    'Content-Type': 'application/pdf',
    'Content-Disposition': `attachment; filename="invoice-${data.invoice.invoice_number}.pdf"`,
  },
})
```

### 4. Add PDF Download Button

Update the invoice detail page to enable PDF download:

```typescript
const handleDownloadPDF = async () => {
  try {
    const response = await fetch(`/api/invoices/${invoice.id}/pdf`)
    const blob = await response.blob()
    
    // Create download link
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `invoice-${invoice.invoice_number}.pdf`
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
  } catch (error) {
    console.error('Failed to download PDF:', error)
  }
}
```

## 🚀 **Production Deployment**

### Font Loading Best Practices

The PDF generator now includes robust font loading to ensure consistent typography in production:

1. **Google Fonts Integration**: Inter font is loaded directly from Google Fonts
2. **Font Loading Verification**: Waits for fonts to load before PDF generation
3. **Comprehensive Fallbacks**: Multiple fallback fonts for reliability
4. **Font Smoothing**: Optimized rendering with antialiasing

### Vercel Deployment

Add to `vercel.json`:

```json
{
  "functions": {
    "src/app/api/invoices/[id]/pdf/route.ts": {
      "maxDuration": 30
    }
  }
}
```

### Docker Deployment

Add to `Dockerfile`:

```dockerfile
# Install Chrome dependencies and fonts
RUN apt-get update && apt-get install -y \
    chromium \
    fonts-liberation \
    fonts-noto-color-emoji \
    fonts-noto-cjk \
    fonts-inter \
    && rm -rf /var/lib/apt/lists/*

# Set Chrome path
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
```

### PM2/Nginx Production Setup

For PM2 with nginx deployment:

1. **Ensure Font Access**: Make sure the server can access Google Fonts
2. **Network Configuration**: Allow outbound HTTPS connections
3. **Chrome Flags**: Use the optimized Chrome flags in the generator
4. **Memory Management**: Monitor memory usage for PDF generation

## 🎨 **Customization Options**

### Custom Page Formats

```typescript
const customOptions: PDFGenerationOptions = {
  format: 'A4',
  orientation: 'portrait',
  margin: {
    top: '25mm',
    right: '20mm',
    bottom: '25mm',
    left: '20mm'
  },
  scale: 0.9
}
```

### Header/Footer Templates

```typescript
const headerTemplate = `
  <div style="font-size: 10px; text-align: center; width: 100%;">
    <span class="title"></span>
  </div>
`

const footerTemplate = `
  <div style="font-size: 10px; text-align: center; width: 100%;">
    Page <span class="pageNumber"></span> of <span class="totalPages"></span>
  </div>
`
```

## 🔧 **Troubleshooting**

### Common Issues

1. **Chrome not found**: Install Chrome/Chromium
2. **Fonts not loading**:
   - Check network access to Google Fonts
   - Verify font loading in browser console
   - Ensure proper font fallbacks
3. **Memory issues**: Increase Node.js memory limit
4. **Timeout errors**: Increase PDF generation timeout
5. **Font rendering inconsistencies**:
   - Verify Inter font is loading properly
   - Check font-display settings
   - Test with different network conditions

### Font-Specific Troubleshooting

#### Inter Font Not Loading
```bash
# Test font access
curl -I https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700

# Check if fonts are cached
ls ~/.cache/puppeteer/
```

#### Production Font Issues
1. **Network Access**: Ensure server can reach fonts.googleapis.com
2. **Font Caching**: Consider local font hosting for critical environments
3. **Fallback Testing**: Test with network disabled to verify fallbacks work

### Performance Optimization

```typescript
// Reuse browser instance for multiple PDFs
class PDFService {
  private static browser: Browser | null = null
  
  static async getBrowser() {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      })
    }
    return this.browser
  }
}
```

## 📋 **Testing**

### Test PDF Generation

```bash
# Test API endpoint
curl -o test-invoice.pdf http://localhost:3000/api/invoices/[invoice-id]/pdf

# Test in browser
http://localhost:3000/api/invoices/[invoice-id]/pdf
```

### Quality Checklist

- ✅ Indonesian Rupiah formatting
- ✅ Company logo display
- ✅ Professional typography
- ✅ Proper page breaks
- ✅ Print-ready margins
- ✅ Bilingual support
- ✅ Bank account information
- ✅ Milestone information

## 🌟 **Features**

### Current Implementation

- ✅ Professional invoice layout
- ✅ Indonesian business compliance
- ✅ Company branding support
- ✅ Payment milestone display
- ✅ Bank account information
- ✅ Bilingual text support
- ✅ High-quality typography
- ✅ Print-optimized styling

### Future Enhancements

- 🔄 Batch PDF generation
- 🔄 Email PDF attachments
- 🔄 Custom templates
- 🔄 Digital signatures
- 🔄 QR code integration
- 🔄 Multi-language support

## 📞 **Support**

If you encounter issues with PDF generation:

1. Check browser console for errors
2. Verify Puppeteer installation
3. Test with simple HTML first
4. Check server logs for detailed errors
5. Ensure sufficient memory allocation

The PDF generation system is designed to produce professional, high-quality invoices suitable for Indonesian business requirements.
